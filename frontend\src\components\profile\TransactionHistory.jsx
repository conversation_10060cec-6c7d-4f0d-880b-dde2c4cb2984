import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaSearch, FaFilter, FaCheckCircle, FaTimesCircle, FaHourglassHalf } from 'react-icons/fa';
import { useTransactions } from '../../hooks';
import { formatCurrency, formatDate } from '../../utils/format';

const TransactionHistory = () => {
  const { transactions, pagination, isLoading, error, fetchMyTransactions } = useTransactions();
  const [filters, setFilters] = useState({
    status: '',
    page: 1,
    limit: 10
  });

  // L<PERSON>y danh sách giao dịch khi component mount hoặc filters thay đổi
  useEffect(() => {
    const loadTransactions = async () => {
      try {
        await fetchMyTransactions(filters);
      } catch (err) {
        toast.error('<PERSON>hông thể tải lịch sử giao dịch');
      }
    };

    loadTransactions();
  }, [fetchMyTransactions, filters]);

  // X<PERSON> lý thay đổi bộ lọc
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
      page: 1 // Reset về trang 1 khi thay đổi bộ lọc
    });
  };

  // Xử lý thay đổi trang
  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= pagination.totalPages) {
      setFilters({
        ...filters,
        page: newPage
      });
    }
  };

  // Hiển thị trạng thái giao dịch
  const renderStatus = (status) => {
    switch (status) {
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <FaCheckCircle className="mr-1" />
            Thành công
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <FaTimesCircle className="mr-1" />
            Thất bại
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <FaHourglassHalf className="mr-1" />
            Đang xử lý
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Không xác định
          </span>
        );
    }
  };

  // Hiển thị loại gói
  const renderPackageType = (packageType) => {
    switch (packageType) {
      case '1_week':
        return '1 tuần';
      case '2_weeks':
        return '2 tuần';
      case '1_month':
        return '1 tháng';
      default:
        return packageType;
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6 pb-2 border-b">Lịch sử giao dịch</h2>

      {/* Bộ lọc */}
      <div className="mb-6 bg-gray-50 p-4 rounded-lg">
        <div className="flex flex-col md:flex-row gap-4 items-end">
          <div className="flex-1">
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              <FaFilter className="inline mr-1" />
              Trạng thái
            </label>
            <select
              id="status"
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
              className="focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md py-2"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="completed">Thành công</option>
              <option value="pending">Đang xử lý</option>
              <option value="failed">Thất bại</option>
            </select>
          </div>

          {/* <div className="flex-1">
            <label htmlFor="limit" className="block text-sm font-medium text-gray-700 mb-1">
              Số lượng hiển thị
            </label>
            <select
              id="limit"
              name="limit"
              value={filters.limit}
              onChange={handleFilterChange}
              className="focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md py-2"
            >
              <option value="5">5 giao dịch</option>
              <option value="10">10 giao dịch</option>
              <option value="20">20 giao dịch</option>
              <option value="50">50 giao dịch</option>
            </select>
          </div> */}
        </div>
      </div>

      {/* Danh sách giao dịch */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500">{error}</p>
          <button
            onClick={() => fetchMyTransactions(filters)}
            className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Thử lại
          </button>
        </div>
      ) : !transactions || !Array.isArray(transactions) || transactions.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-600">Bạn chưa có giao dịch nào</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mã giao dịch
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phòng trọ
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Số tiền
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Gói tin
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ngày tạo
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {transactions && Array.isArray(transactions) && transactions.map((transaction) => (
                <tr key={transaction._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {transaction.vnpayTxnRef || transaction._id.substring(0, 8)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {transaction.room ? (
                      <Link to={`/rooms/${transaction.room._id}`} className="text-primary hover:underline">
                        {transaction.room.title}
                      </Link>
                    ) : (
                      'Không có thông tin'
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(transaction.amount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {renderPackageType(transaction.packageType)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStatus(transaction.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(transaction.createdAt)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Phân trang */}
      {!isLoading && !error && transactions && Array.isArray(transactions) && transactions.length > 0 && (
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-700">
            Hiển thị <span className="font-medium">{(pagination?.page - 1) * pagination?.limit + 1}</span> đến{' '}
            <span className="font-medium">
              {Math.min(pagination?.page * pagination?.limit, pagination?.totalResults)}
            </span>{' '}
            trong tổng số <span className="font-medium">{pagination?.totalResults}</span> giao dịch
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => handlePageChange(pagination?.page - 1)}
              disabled={pagination?.page === 1}
              className={`px-3 py-1 rounded-md ${
                pagination?.page === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-primary hover:bg-gray-50 border border-gray-300'
              }`}
            >
              Trước
            </button>

            {Array.from({ length: pagination?.totalPages || 0 }, (_, i) => i + 1)
              .filter(page => {
                // Hiển thị trang hiện tại, trang đầu, trang cuối và các trang lân cận
                return (
                  page === 1 ||
                  page === pagination?.totalPages ||
                  Math.abs(page - (pagination?.page || 1)) <= 1
                );
              })
              .map((page, index, array) => {
                // Thêm dấu ... nếu có khoảng cách giữa các trang
                const showEllipsis = index > 0 && page - array[index - 1] > 1;

                return (
                  <div key={page} className="flex items-center">
                    {showEllipsis && (
                      <span className="px-3 py-1 text-gray-500">...</span>
                    )}

                    <button
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-1 rounded-md ${
                        pagination?.page === page
                          ? 'bg-primary text-white'
                          : 'bg-white text-primary hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {page}
                    </button>
                  </div>
                );
              })}

            <button
              onClick={() => handlePageChange(pagination?.page + 1)}
              disabled={pagination?.page === pagination?.totalPages}
              className={`px-3 py-1 rounded-md ${
                pagination?.page === pagination?.totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-primary hover:bg-gray-50 border border-gray-300'
              }`}
            >
              Tiếp
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionHistory;
