# HOẠT ĐỘNG THANH TOÁN GÓI TIN NỔI BẬT

## Tổng quan hệ thống thanh toán

Hệ thống thanh toán gói tin nổi bật được thiết kế để cho phép chủ trọ nâng cấp bài đăng phòng trọ của mình lên gói tin nổi bật thông qua cổng thanh toán VNPay. Quá trình này giúp tăng khả năng hiển thị và thu hút người thuê phòng hiệu quả hơn.

## Quy trình thanh toán chi tiết

### 1. Khởi tạo yêu cầu thanh toán

**Bước 1: Chủ trọ chọn nâng cấp gói tin**
- Chủ trọ truy cập vào trang "Phòng trọ của tôi"
- Chọn phòng trọ cần nâng cấp
- Nhấn vào nút "Nâng cấp gói tin" trong menu thao tác
- Hệ thống chuyển hướng đến trang `/rooms/upgrade/{roomId}`

**Bước 2: <PERSON><PERSON><PERSON> nhận thông tin gói tin**
- Hiển thị thông tin chi tiết phòng trọ
- Hiển thị giá gói tin nổi bật (50.000 VNĐ)
- Chủ trọ xác nhận thông tin và nhấn "Thanh toán"

### 2. Tạo giao dịch thanh toán

**Bước 3: Gửi yêu cầu tạo giao dịch**
```
POST /api/payments/create
{
  "roomId": "string",
  "amount": 50000,
  "packageType": "highlight"
}
```

**Bước 4: Xử lý tạo giao dịch (Backend)**
- Kiểm tra quyền sở hữu phòng trọ
- Kiểm tra trạng thái phòng trọ (phải là 'available')
- Kiểm tra phòng chưa được nâng cấp trước đó
- Tạo bản ghi Transaction với trạng thái 'pending'
- Tạo URL thanh toán VNPay với các tham số:
  - `vnp_Amount`: 5000000 (50.000 VNĐ * 100)
  - `vnp_OrderInfo`: "Nang cap goi tin noi bat - Room ID: {roomId}"
  - `vnp_TxnRef`: Transaction ID
  - `vnp_ReturnUrl`: URL callback xử lý kết quả

### 3. Chuyển hướng đến VNPay

**Bước 5: Redirect đến cổng thanh toán**
- Backend trả về `paymentUrl` từ VNPay
- Frontend chuyển hướng người dùng đến VNPay
- Người dùng thực hiện thanh toán trên giao diện VNPay

### 4. Xử lý kết quả thanh toán

**Bước 6: VNPay callback (IPN)**
```
GET /api/payments/vnpay-ipn?vnp_ResponseCode=00&vnp_TxnRef=...
```

**Bước 7: Xử lý kết quả thanh toán**
- Xác thực chữ ký số từ VNPay
- Kiểm tra mã phản hồi (`vnp_ResponseCode`)
- Cập nhật trạng thái giao dịch:
  - `vnp_ResponseCode = "00"`: Thành công → `status = "completed"`
  - Khác: Thất bại → `status = "failed"`

**Bước 8: Cập nhật trạng thái phòng trọ (nếu thành công)**
- Cập nhật `isHighlighted = true` cho phòng trọ
- Ghi log hoạt động nâng cấp
- Gửi thông báo thành công cho chủ trọ

### 5. Xử lý return URL

**Bước 9: Người dùng quay lại website**
```
GET /payment-result?vnp_ResponseCode=00&vnp_TxnRef=...
```

**Bước 10: Hiển thị kết quả**
- Kiểm tra trạng thái giao dịch từ database
- Hiển thị thông báo thành công/thất bại
- Chuyển hướng về trang quản lý phòng trọ

## Cấu trúc dữ liệu

### Transaction Model
```javascript
{
  _id: ObjectId,
  user: ObjectId,           // Chủ trọ
  room: ObjectId,           // Phòng trọ được nâng cấp
  amount: Number,           // Số tiền (50000)
  type: String,             // "room_upgrade"
  status: String,           // "pending", "completed", "failed"
  paymentMethod: String,    // "vnpay"
  vnpayData: {
    vnp_TxnRef: String,     // Mã giao dịch
    vnp_ResponseCode: String,
    vnp_TransactionNo: String,
    vnp_BankCode: String
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Room Model (cập nhật)
```javascript
{
  // ... các trường khác
  isHighlighted: Boolean,   // Trạng thái tin nổi bật
  // Không sử dụng highlightExpiry (gói vĩnh viễn)
}
```

## Xử lý lỗi và ngoại lệ

### Lỗi phía người dùng
- **Phòng không tồn tại**: Trả về lỗi 404
- **Không có quyền**: Trả về lỗi 403
- **Phòng đã được nâng cấp**: Trả về lỗi 400
- **Phòng không ở trạng thái available**: Trả về lỗi 400

### Lỗi thanh toán
- **Thanh toán thất bại**: Giữ nguyên trạng thái phòng, cập nhật transaction status
- **Timeout**: Tự động hủy giao dịch sau 15 phút
- **Lỗi kết nối VNPay**: Retry mechanism với exponential backoff

### Xử lý giao dịch trùng lặp
- Kiểm tra `vnp_TxnRef` đã tồn tại
- Chỉ xử lý giao dịch một lần duy nhất
- Log tất cả các request để audit

## Bảo mật

### Xác thực VNPay
- Sử dụng HMAC SHA512 để xác thực chữ ký
- Kiểm tra `vnp_SecureHash` trong mọi request
- Validate tất cả tham số từ VNPay

### Bảo vệ dữ liệu
- Mã hóa thông tin nhạy cảm
- Không lưu trữ thông tin thẻ
- Log audit cho tất cả giao dịch

## Monitoring và Logging

### Metrics theo dõi
- Tỷ lệ thành công thanh toán
- Thời gian xử lý giao dịch
- Số lượng giao dịch theo ngày/tháng
- Revenue từ gói tin nổi bật

### Logging
- Log tất cả request/response với VNPay
- Log thay đổi trạng thái giao dịch
- Log lỗi và exception
- Log hoạt động người dùng

## Quy trình rollback

### Trường hợp cần rollback
- Phát hiện giao dịch gian lận
- Yêu cầu hoàn tiền từ khách hàng
- Lỗi hệ thống sau khi thanh toán

### Các bước rollback
1. Cập nhật `isHighlighted = false`
2. Tạo transaction hoàn tiền
3. Thông báo cho chủ trọ
4. Ghi log hoạt động rollback

## Tối ưu hóa hiệu suất

### Caching
- Cache thông tin gói tin và giá
- Cache trạng thái phòng trọ
- Cache kết quả validation

### Database optimization
- Index trên các trường thường query
- Partition transaction table theo thời gian
- Cleanup dữ liệu cũ định kỳ

## Kế hoạch mở rộng

### Tính năng tương lai
- Nhiều gói tin khác nhau (Premium, VIP)
- Thanh toán định kỳ
- Hệ thống điểm thưởng
- Tích hợp thêm cổng thanh toán khác

### Scalability
- Microservices architecture cho payment
- Message queue cho xử lý bất đồng bộ
- Load balancing cho high availability
