# HOẠT ĐỘNG THANH TOÁN GÓI TIN NỔI BẬT

## Giới thiệu về luồng thanh toán

Chào các bạn! Hôm nay tôi sẽ trình bày về cách thức hoạt động của hệ thống thanh toán gói tin nổi bật trong ứng dụng tìm phòng trọ. Đây là một tính năng cho phép chủ trọ nâng cấp bài đăng của mình để có độ ưu tiên hiển thị cao hơn.

Toàn bộ quá trình này được thiết kế theo mô hình tích hợp với cổng thanh toán <PERSON>, đảm bảo tính bảo mật và tin cậy cho người dùng.

## Luồng hoạt động chi tiết từ Frontend đến Backend

### Bước 1: Khởi tạo yêu cầu nâng cấp từ Frontend

Đầu tiê<PERSON>, chúng ta bắt đầu từ trang MyRoomsPage. <PERSON>hi chủ trọ muốn nâng cấp một căn phòng, họ sẽ:

1. **Truy cập trang quản lý:** Vào `/my-rooms` để xem danh sách phòng trọ
2. **Chọn phòng cần nâng cấp:** Click vào menu dropdown của phòng cần nâng cấp
3. **Nhấn "Nâng cấp gói tin":** Hệ thống sẽ navigate đến `/rooms/upgrade/{roomId}`

Tại trang upgrade, component `RoomUpgradePage` sẽ:
- Hiển thị thông tin chi tiết phòng trọ
- Hiển thị giá gói tin nổi bật (50.000 VNĐ)
- Có nút "Thanh toán" để khởi tạo giao dịch

### Bước 2: Gọi API tạo giao dịch thanh toán

Khi người dùng nhấn "Thanh toán", Frontend sẽ gọi API:

```javascript
POST /api/payments/create
Content-Type: application/json
Authorization: Bearer {token}

{
  "roomId": "507f1f77bcf86cd799439011",
  "amount": 50000,
  "packageType": "highlight"
}
```

API này được xử lý bởi `paymentController.createPayment()` trong backend.

### Bước 3: Xử lý logic nghiệp vụ tại Backend

Trong controller `createPayment`, hệ thống sẽ thực hiện các bước validation:

**Kiểm tra quyền sở hữu:**
```javascript
const room = await Room.findById(roomId);
if (room.user.toString() !== req.user._id.toString()) {
  return res.status(403).json({ message: 'Không có quyền' });
}
```

**Kiểm tra trạng thái phòng:**
```javascript
if (room.status !== 'available') {
  return res.status(400).json({ message: 'Phòng phải ở trạng thái available' });
}
```

**Kiểm tra đã nâng cấp chưa:**
```javascript
if (room.isHighlighted) {
  return res.status(400).json({ message: 'Phòng đã được nâng cấp' });
}
```

### Bước 4: Tạo Transaction và URL VNPay

Sau khi validation thành công, hệ thống sẽ:

**Tạo bản ghi Transaction:**
```javascript
const transaction = new Transaction({
  user: req.user._id,
  room: roomId,
  amount: 50000,
  type: 'room_upgrade',
  status: 'pending',
  paymentMethod: 'vnpay'
});
await transaction.save();
```

**Tạo URL thanh toán VNPay:**
```javascript
const vnpayParams = {
  vnp_Amount: 5000000, // 50.000 * 100
  vnp_Command: 'pay',
  vnp_CreateDate: moment().format('YYYYMMDDHHmmss'),
  vnp_CurrCode: 'VND',
  vnp_IpAddr: req.ip,
  vnp_Locale: 'vn',
  vnp_OrderInfo: `Nang cap goi tin noi bat - Room ID: ${roomId}`,
  vnp_OrderType: 'other',
  vnp_ReturnUrl: `${process.env.CLIENT_URL}/payment-result`,
  vnp_TxnRef: transaction._id.toString(),
  vnp_Version: '2.1.0'
};
```

### Bước 5: Response và chuyển hướng

Backend trả về response:
```javascript
res.status(200).json({
  success: true,
  paymentUrl: vnpayUrl,
  transactionId: transaction._id
});
```

Frontend nhận được `paymentUrl` và thực hiện:
```javascript
window.location.href = response.data.paymentUrl;
```

### Bước 6: Xử lý thanh toán tại VNPay

Lúc này người dùng đã được chuyển sang trang VNPay để thực hiện thanh toán. VNPay sẽ xử lý giao dịch và có hai cách thông báo kết quả về cho hệ thống của chúng ta:

**IPN (Instant Payment Notification):** VNPay sẽ gọi callback đến endpoint:
```
GET /api/payments/vnpay-ipn?vnp_ResponseCode=00&vnp_TxnRef=...
```

**Return URL:** Sau khi thanh toán, VNPay sẽ redirect người dùng về:
```
GET /payment-result?vnp_ResponseCode=00&vnp_TxnRef=...
```

### Bước 7: Xử lý IPN callback từ VNPay

Đây là phần quan trọng nhất! Khi VNPay gọi IPN, controller `vnpayIPN` sẽ thực hiện:

**Xác thực chữ ký:**
```javascript
const secureHash = req.query.vnp_SecureHash;
const signData = querystring.stringify(vnpParams, { encode: false });
const hmac = crypto.createHmac('sha512', process.env.VNP_HASH_SECRET);
const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

if (secureHash !== signed) {
  return res.status(400).json({ message: 'Invalid signature' });
}
```

**Cập nhật trạng thái giao dịch:**
```javascript
const transaction = await Transaction.findById(vnp_TxnRef);

if (vnp_ResponseCode === '00') {
  // Thanh toán thành công
  transaction.status = 'completed';
  transaction.vnpayData = {
    vnp_ResponseCode,
    vnp_TransactionNo,
    vnp_BankCode
  };

  // Cập nhật phòng trọ
  await Room.findByIdAndUpdate(transaction.room, {
    isHighlighted: true
  });
} else {
  // Thanh toán thất bại
  transaction.status = 'failed';
}

await transaction.save();
```

### Bước 8: Xử lý Return URL tại Frontend

Khi người dùng được redirect về `/payment-result`, component `PaymentResultPage` sẽ:

**Parse URL parameters:**
```javascript
const urlParams = new URLSearchParams(window.location.search);
const responseCode = urlParams.get('vnp_ResponseCode');
const txnRef = urlParams.get('vnp_TxnRef');
```

**Gọi API kiểm tra trạng thái:**
```javascript
GET /api/payments/status/${txnRef}
```

**Hiển thị kết quả:**
```javascript
if (responseCode === '00') {
  showSuccessMessage('Nâng cấp gói tin thành công!');
} else {
  showErrorMessage('Thanh toán thất bại. Vui lòng thử lại.');
}
```

## Cấu trúc dữ liệu trong Database

### Transaction Schema
```javascript
const transactionSchema = {
  _id: ObjectId,
  user: { type: ObjectId, ref: 'User' },
  room: { type: ObjectId, ref: 'Room' },
  amount: Number, // 50000
  type: String,   // 'room_upgrade'
  status: String, // 'pending', 'completed', 'failed'
  paymentMethod: String, // 'vnpay'
  vnpayData: {
    vnp_TxnRef: String,
    vnp_ResponseCode: String,
    vnp_TransactionNo: String,
    vnp_BankCode: String
  },
  createdAt: Date,
  updatedAt: Date
};
```

### Room Schema (phần liên quan)
```javascript
const roomSchema = {
  // ... các trường khác
  isHighlighted: {
    type: Boolean,
    default: false
  }
  // Không sử dụng highlightExpiry vì gói vĩnh viễn
};
```

## Xử lý các trường hợp ngoại lệ

### Validation Errors
Trong quá trình xử lý, chúng ta cần handle các lỗi sau:

```javascript
// Phòng không tồn tại
if (!room) {
  return res.status(404).json({
    success: false,
    message: 'Phòng trọ không tồn tại'
  });
}

// Không có quyền sở hữu
if (room.user.toString() !== req.user._id.toString()) {
  return res.status(403).json({
    success: false,
    message: 'Bạn không có quyền nâng cấp phòng này'
  });
}

// Phòng đã được nâng cấp
if (room.isHighlighted) {
  return res.status(400).json({
    success: false,
    message: 'Phòng đã được nâng cấp trước đó'
  });
}
```

### Timeout và Retry Logic
```javascript
// Tự động hủy giao dịch sau 15 phút
setTimeout(async () => {
  const transaction = await Transaction.findById(transactionId);
  if (transaction.status === 'pending') {
    transaction.status = 'expired';
    await transaction.save();
  }
}, 15 * 60 * 1000);
```

### Duplicate Transaction Prevention
```javascript
// Kiểm tra giao dịch trùng lặp
const existingTransaction = await Transaction.findOne({
  'vnpayData.vnp_TxnRef': vnp_TxnRef,
  status: 'completed'
});

if (existingTransaction) {
  return res.status(200).json({ message: 'Transaction already processed' });
}
```
## Bảo mật và Monitoring

### Cơ chế bảo mật VNPay
Như các bạn thấy, việc xác thực chữ ký là rất quan trọng:

```javascript
// Tạo chữ ký khi gửi request
const signData = querystring.stringify(vnpParams, { encode: false });
const hmac = crypto.createHmac('sha512', VNP_HASH_SECRET);
const vnp_SecureHash = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');
```

Điều này đảm bảo:
- Dữ liệu không bị thay đổi trong quá trình truyền tải
- Chỉ VNPay mới có thể tạo ra chữ ký hợp lệ
- Ngăn chặn các cuộc tấn công giả mạo

### Logging và Audit Trail
Hệ thống ghi log tất cả các hoạt động:

```javascript
// Log mọi request từ VNPay
logger.info('VNPay IPN received', {
  vnp_TxnRef,
  vnp_ResponseCode,
  vnp_Amount,
  timestamp: new Date()
});

// Log thay đổi trạng thái
logger.info('Transaction status updated', {
  transactionId,
  oldStatus: 'pending',
  newStatus: 'completed',
  userId: transaction.user
});
```

### Metrics và Monitoring
Chúng ta theo dõi các chỉ số quan trọng:

```javascript
// Success rate
const successRate = completedTransactions / totalTransactions * 100;

// Average processing time
const avgProcessingTime = totalProcessingTime / totalTransactions;

// Revenue tracking
const dailyRevenue = await Transaction.aggregate([
  { $match: { status: 'completed', createdAt: { $gte: startOfDay } } },
  { $group: { _id: null, total: { $sum: '$amount' } } }
]);
```

## Quy trình Rollback và Recovery

### Khi cần hoàn tiền
Đôi khi chúng ta cần thực hiện rollback:

```javascript
async function rollbackTransaction(transactionId, reason) {
  const transaction = await Transaction.findById(transactionId);

  // Cập nhật trạng thái phòng
  await Room.findByIdAndUpdate(transaction.room, {
    isHighlighted: false
  });

  // Tạo bản ghi hoàn tiền
  const refund = new Transaction({
    user: transaction.user,
    room: transaction.room,
    amount: -transaction.amount,
    type: 'refund',
    status: 'completed',
    originalTransaction: transactionId,
    reason: reason
  });

  await refund.save();

  // Gửi thông báo
  await sendRefundNotification(transaction.user, transaction.amount);
}
```

### Error Recovery
```javascript
// Retry mechanism cho failed transactions
async function retryFailedTransaction(transactionId) {
  const transaction = await Transaction.findById(transactionId);

  if (transaction.retryCount < 3) {
    transaction.retryCount += 1;
    transaction.status = 'pending';
    await transaction.save();

    // Tạo lại URL VNPay
    const newPaymentUrl = await createVNPayUrl(transaction);
    return newPaymentUrl;
  } else {
    transaction.status = 'failed';
    await transaction.save();
    throw new Error('Maximum retry attempts exceeded');
  }
}
```

## Tối ưu hóa Performance

### Database Indexing
```javascript
// Indexes cho Transaction collection
db.transactions.createIndex({ "user": 1, "createdAt": -1 });
db.transactions.createIndex({ "vnpayData.vnp_TxnRef": 1 });
db.transactions.createIndex({ "status": 1, "createdAt": -1 });

// Indexes cho Room collection
db.rooms.createIndex({ "user": 1, "isHighlighted": -1 });
```

### Caching Strategy
```javascript
// Cache thông tin gói tin
const packageInfo = await redis.get('package:highlight');
if (!packageInfo) {
  const info = { price: 50000, duration: 'permanent' };
  await redis.setex('package:highlight', 3600, JSON.stringify(info));
}

// Cache user permissions
const userPermissions = await redis.get(`user:${userId}:permissions`);
```

## Kế hoạch mở rộng tương lai

### Multiple Payment Gateways
```javascript
// Factory pattern cho payment providers
class PaymentFactory {
  static createProvider(type) {
    switch(type) {
      case 'vnpay': return new VNPayProvider();
      case 'momo': return new MoMoProvider();
      case 'zalopay': return new ZaloPayProvider();
      default: throw new Error('Unsupported payment provider');
    }
  }
}
```

### Subscription Model
```javascript
// Schema cho subscription packages
const subscriptionSchema = {
  user: ObjectId,
  package: String, // 'basic', 'premium', 'vip'
  startDate: Date,
  endDate: Date,
  autoRenew: Boolean,
  status: String // 'active', 'expired', 'cancelled'
};
```

### Microservices Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Room Service  │    │ Payment Service │    │ Notification    │
│                 │    │                 │    │ Service         │
│ - Room CRUD     │◄──►│ - VNPay         │◄──►│ - Email         │
│ - Highlighting  │    │ - MoMo          │    │ - SMS           │
│ - Search        │    │ - ZaloPay       │    │ - Push          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Kết luận

Như vậy, toàn bộ luồng thanh toán gói tin nổi bật bao gồm 8 bước chính:

1. **Frontend initiation** - Người dùng chọn nâng cấp
2. **API call** - Gọi `/api/payments/create`
3. **Backend validation** - Kiểm tra quyền và trạng thái
4. **Transaction creation** - Tạo bản ghi giao dịch
5. **VNPay URL generation** - Tạo link thanh toán
6. **Payment processing** - Xử lý tại VNPay
7. **IPN handling** - Nhận và xử lý callback
8. **Result display** - Hiển thị kết quả cho người dùng

Hệ thống được thiết kế với tính bảo mật cao, khả năng mở rộng tốt và có cơ chế xử lý lỗi toàn diện. Cảm ơn các bạn đã theo dõi!
