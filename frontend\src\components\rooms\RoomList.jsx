import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  FaEdit, FaTrash, FaEllipsisV, FaCheckCircle,
  FaTimesCircle, FaEyeSlash, FaChartLine, FaExternalLinkAlt, FaCrown
} from 'react-icons/fa';
import { formatCurrency, formatDate } from '../../utils/format';
import { ImageWithFallback } from '../../components';
import { useRooms } from '../../hooks';

const RoomList = ({ rooms, pagination, isLoading, onStatusUpdate, onRoomDeleted }) => {
  const { deleteRoom } = useRooms();
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [roomToDelete, setRoomToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Xử lý hiển thị/ẩn dropdown
  const toggleDropdown = (roomId, e) => {
    e.stopPropagation(); // Ngăn chặn sự kiện click lan ra ngoài
    setActiveDropdown(activeDropdown === roomId ? null : roomId);
  };

  // Xử lý đóng dropdown khi click ra ngoài
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Nếu click ra ngoài dropdown đang mở, đóng nó lại
      if (activeDropdown && !event.target.closest('.dropdown-menu-container')) {
        // Thêm hiệu ứng đóng mượt mà
        setActiveDropdown(null);
      }
    };

    // Xử lý đóng dropdown khi nhấn phím Escape
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && activeDropdown) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscKey);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [activeDropdown]);

  // Xử lý xóa phòng
  const handleDelete = (room) => {
    setRoomToDelete(room);
  };

  // Xác nhận xóa phòng
  const confirmDelete = async () => {
    if (!roomToDelete) return;

    setIsDeleting(true);
    try {
      console.log('Đang xóa phòng trọ:', roomToDelete._id);

      // Gọi API xóa phòng
      await deleteRoom(roomToDelete._id);

      toast.success(`Xóa phòng trọ "${roomToDelete.title}" thành công`);
      setRoomToDelete(null);
      setActiveDropdown(null);

      // Gọi callback để cập nhật lại danh sách phòng ở component cha
      if (onRoomDeleted) {
        onRoomDeleted(roomToDelete._id);
      }
    } catch (err) {
      console.error('Lỗi khi xóa phòng trọ:', err);
      const errorMessage = err?.message || 'Không thể xóa phòng trọ. Vui lòng thử lại.';
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  // Xử lý cập nhật trạng thái
  const handleStatusUpdate = (roomId, newStatus) => {
    onStatusUpdate(roomId, newStatus);
    setActiveDropdown(null);
  };

  // Hiển thị trạng thái phòng
  const renderStatus = (status) => {
    switch (status) {
      case 'available':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <FaCheckCircle className="mr-1" />
            Còn trống
          </span>
        );
      case 'rented':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <FaTimesCircle className="mr-1" />
            Đã cho thuê
          </span>
        );
      case 'hidden':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <FaEyeSlash className="mr-1" />
            Đã ẩn
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Không xác định
          </span>
        );
    }
  };

  // Hiển thị loading
  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
      </div>
    );
  }

  // Hiển thị khi không có phòng trọ
  if (rooms.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <p className="text-gray-600 mb-4">Bạn chưa có phòng trọ nào</p>
        <Link
          to="/rooms/create"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          Đăng tin ngay
        </Link>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phòng trọ
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Giá
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ngày đăng
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {rooms.map((room) => (
                <tr key={room._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-16 w-16 rounded-md overflow-hidden">
                        <ImageWithFallback
                          src={room.images && room.images.length > 0 ? room.images[0] : ''}
                          alt={room.title}
                          className="h-16 w-16 object-cover"
                          fallbackSrc="https://via.placeholder.com/150x150?text=Không+có+hình"
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 line-clamp-1">
                          {room.title}
                        </div>
                        <div className="text-sm text-gray-500 line-clamp-1">
                          {room.address?.district}, {room.address?.city}
                        </div>
                        {room.isHighlighted && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-1">
                            Tin nổi bật
                          </span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{formatCurrency(room.price)}</div>
                    <div className="text-sm text-gray-500">{room.area} m²</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStatus(room.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(room.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="relative dropdown-menu-container">
                      <button
                        onClick={(e) => toggleDropdown(room._id, e)}
                        className="p-1.5 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 transition-colors"
                        aria-label="Mở menu thao tác"
                        title="Thao tác"
                      >
                        <FaEllipsisV />
                      </button>

                      <div
                        className={`absolute right-0 top-full mt-1 w-56 rounded-md shadow-xl bg-white ring-1 ring-black ring-opacity-5 z-[100] max-h-[80vh] overflow-y-auto transform origin-top-right transition-all duration-200 ease-in-out ${
                          activeDropdown === room._id ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none'
                        }`}
                      >
                        <div className="py-1" role="menu" aria-orientation="vertical">
                            <Link
                              to={`/rooms/${room._id}`}
                              className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors"
                              role="menuitem"
                            >
                              <FaExternalLinkAlt className="mr-3 text-gray-500" />
                              Xem chi tiết
                            </Link>
                            <Link
                              to={`/rooms/edit/${room._id}`}
                              className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors"
                              role="menuitem"
                            >
                              <FaEdit className="mr-3 text-gray-500" />
                              Chỉnh sửa
                            </Link>

                            <Link
                              to={`/rooms/upgrade/${room._id}`}
                              className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-yellow-50 hover:text-yellow-700 transition-colors"
                              role="menuitem"
                            >
                              <FaCrown className="mr-3 text-yellow-500" />
                              Nâng cấp gói tin
                            </Link>

                            <div className="border-t border-gray-100 my-1"></div>

                            {room.status !== 'available' && (
                              <button
                                onClick={() => handleStatusUpdate(room._id, 'available')}
                                className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 w-full text-left transition-colors"
                                role="menuitem"
                              >
                                <FaCheckCircle className="mr-3 text-green-500" />
                                Đánh dấu còn trống
                              </button>
                            )}

                            {room.status !== 'rented' && (
                              <button
                                onClick={() => handleStatusUpdate(room._id, 'rented')}
                                className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 w-full text-left transition-colors"
                                role="menuitem"
                              >
                                <FaTimesCircle className="mr-3 text-blue-500" />
                                Đánh dấu đã cho thuê
                              </button>
                            )}

                            {room.status !== 'hidden' && (
                              <button
                                onClick={() => handleStatusUpdate(room._id, 'hidden')}
                                className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-700 w-full text-left transition-colors"
                                role="menuitem"
                              >
                                <FaEyeSlash className="mr-3 text-gray-500" />
                                Ẩn tin đăng
                              </button>
                            )}

                            <div className="border-t border-gray-100 my-1"></div>

                            <button
                              onClick={() => handleDelete(room)}
                              className="flex items-center px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 w-full text-left transition-colors"
                              role="menuitem"
                            >
                              <FaTrash className="mr-3 text-red-500" />
                              Xóa tin đăng
                            </button>
                          </div>
                        </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Hiển thị tổng số phòng trọ */}
      {pagination && pagination.totalResults > 0 && (
        <div className="flex justify-center mt-6">
          <div className="text-sm text-gray-700">
            Tổng số <span className="font-medium">{pagination.totalResults}</span> phòng trọ
          </div>
        </div>
      )}

      {/* Modal xác nhận xóa */}
      {roomToDelete && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <FaTrash className="h-5 w-5 text-red-600" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Xóa tin đăng
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Bạn có chắc chắn muốn xóa tin đăng "{roomToDelete.title}"? Hành động này không thể hoàn tác.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={confirmDelete}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Đang xóa...
                    </>
                  ) : (
                    'Xóa'
                  )}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setRoomToDelete(null)}
                >
                  Hủy
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default RoomList;
